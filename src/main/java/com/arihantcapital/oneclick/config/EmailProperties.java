package com.arihantcapital.oneclick.config;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "email")
@Data
@Component
public class EmailProperties {
    private List<SmtpConfig> providers = new ArrayList<>();
    private String defaultFromName = "Arihant Capital";
    private String defaultFromAddress = "<EMAIL>";
    private int retryAttempts = 3;
    private long retryDelayMs = 1000;

    @Data
    public static class SmtpConfig {
        private String name;
        private String host;
        private int port;
        private String username;
        private String password;
        private boolean enabled = true;
        private int priority = 1;
        private Properties properties = new Properties();
    }
}
