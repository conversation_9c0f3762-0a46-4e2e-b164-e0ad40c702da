package com.arihantcapital.oneclick.core.email;

import com.arihantcapital.oneclick.core.model.email.EmailAttachment;
import com.arihantcapital.oneclick.core.model.email.EmailDeliveryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Example service demonstrating how to use the enhanced EmailManagerService
 * with templates and attachments
 */
@Slf4j
@Service
public class EmailExampleService {
    
    private final EmailManagerService emailManagerService;
    
    public EmailExampleService(EmailManagerService emailManagerService) {
        this.emailManagerService = emailManagerService;
    }
    
    /**
     * Example: Send welcome email with template
     */
    public EmailDeliveryResult sendWelcomeEmail(String userEmail, String userName) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("companyName", "Arihant Capital");
        templateVariables.put("supportEmail", "<EMAIL>");
        
        return emailManagerService.sendEmailWithTemplate(
            "welcome", 
            userEmail, 
            "Welcome to Arihant Capital", 
            templateVariables
        );
    }
    
    /**
     * Example: Send account statement with PDF attachment
     */
    public EmailDeliveryResult sendAccountStatement(String userEmail, String userName, byte[] statementPdf) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("statementMonth", "December 2024");
        templateVariables.put("companyName", "Arihant Capital");
        
        // Create PDF attachment
        EmailAttachment pdfAttachment = EmailManagerService.createPdfAttachment(
            "account_statement_dec_2024.pdf", 
            statementPdf
        );
        
        return emailManagerService.sendEmailWithTemplateAndAttachments(
            "account-statement",
            userEmail,
            "Your Account Statement - December 2024",
            templateVariables,
            Arrays.asList(pdfAttachment)
        );
    }
    
    /**
     * Example: Send KYC documents with multiple attachments
     */
    public EmailDeliveryResult sendKycDocuments(String userEmail, String userName, 
                                               byte[] kycForm, byte[] panCard, byte[] aadhaarCard) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("kycRefNumber", "KYC" + System.currentTimeMillis());
        templateVariables.put("companyName", "Arihant Capital");
        
        // Create multiple attachments
        List<EmailAttachment> attachments = Arrays.asList(
            EmailManagerService.createPdfAttachment("kyc_form.pdf", kycForm),
            EmailManagerService.createImageAttachment("pan_card.jpg", panCard, "jpeg"),
            EmailManagerService.createImageAttachment("aadhaar_card.jpg", aadhaarCard, "jpeg")
        );
        
        return emailManagerService.sendEmailWithTemplateAndAttachments(
            "kyc-documents",
            userEmail,
            "KYC Documents Submission Confirmation",
            templateVariables,
            attachments
        );
    }
    
    /**
     * Example: Send bulk notification with template (async)
     */
    public CompletableFuture<EmailDeliveryResult> sendBulkNotificationAsync(
            List<String> recipients, String notificationType, Map<String, Object> data) {
        
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("notificationType", notificationType);
        templateVariables.put("data", data);
        templateVariables.put("companyName", "Arihant Capital");
        templateVariables.put("timestamp", java.time.LocalDateTime.now());
        
        return emailManagerService.sendEmailWithTemplateAsync(
            "bulk-notification",
            recipients,
            "Important Notification: " + notificationType,
            templateVariables
        );
    }
    
    /**
     * Example: Send trading report with CSV and PDF attachments
     */
    public EmailDeliveryResult sendTradingReport(String userEmail, String userName, 
                                               String csvData, byte[] reportPdf) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("reportDate", java.time.LocalDate.now().toString());
        templateVariables.put("companyName", "Arihant Capital");
        
        // Create attachments
        List<EmailAttachment> attachments = Arrays.asList(
            EmailManagerService.createCsvAttachment("trading_report.csv", csvData),
            EmailManagerService.createPdfAttachment("trading_summary.pdf", reportPdf)
        );
        
        return emailManagerService.sendEmailWithTemplateAndAttachments(
            "trading-report",
            userEmail,
            "Your Trading Report - " + java.time.LocalDate.now(),
            templateVariables,
            attachments
        );
    }
    
    /**
     * Example: Send password reset email with template
     */
    public CompletableFuture<EmailDeliveryResult> sendPasswordResetEmailAsync(
            String userEmail, String userName, String resetToken) {
        
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("resetToken", resetToken);
        templateVariables.put("resetUrl", "https://app.arihantcapital.com/reset-password?token=" + resetToken);
        templateVariables.put("expiryTime", "24 hours");
        templateVariables.put("companyName", "Arihant Capital");
        
        return emailManagerService.sendEmailWithTemplateAsync(
            "password-reset",
            userEmail,
            "Password Reset Request",
            templateVariables
        );
    }
    
    /**
     * Example: Send contract note with attachment
     */
    public EmailDeliveryResult sendContractNote(String userEmail, String userName, 
                                               String tradeDate, byte[] contractNotePdf) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("tradeDate", tradeDate);
        templateVariables.put("companyName", "Arihant Capital");
        
        EmailAttachment contractAttachment = EmailManagerService.createPdfAttachment(
            "contract_note_" + tradeDate.replace("-", "") + ".pdf", 
            contractNotePdf
        );
        
        return emailManagerService.sendEmailWithTemplateAndAttachments(
            "contract-note",
            userEmail,
            "Contract Note - " + tradeDate,
            templateVariables,
            Arrays.asList(contractAttachment)
        );
    }
    
    /**
     * Example: Send email with Excel report attachment
     */
    public EmailDeliveryResult sendExcelReport(String userEmail, String userName, 
                                             String reportType, byte[] excelData) {
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", userName);
        templateVariables.put("reportType", reportType);
        templateVariables.put("generatedDate", java.time.LocalDateTime.now().toString());
        templateVariables.put("companyName", "Arihant Capital");
        
        EmailAttachment excelAttachment = EmailManagerService.createExcelAttachment(
            reportType.toLowerCase().replace(" ", "_") + "_report.xlsx", 
            excelData
        );
        
        return emailManagerService.sendEmailWithTemplateAndAttachments(
            "excel-report",
            userEmail,
            reportType + " Report",
            templateVariables,
            Arrays.asList(excelAttachment)
        );
    }
}
