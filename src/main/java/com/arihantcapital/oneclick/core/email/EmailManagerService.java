package com.arihantcapital.oneclick.core.email;

import com.arihantcapital.oneclick.config.EmailProperties;
import com.arihantcapital.oneclick.core.model.email.EmailDeliveryResult;
import com.arihantcapital.oneclick.core.model.email.EmailRequest;
import com.arihantcapital.oneclick.domain.entity.log.email.EmailDeliveryLog;
import com.arihantcapital.oneclick.domain.entity.log.email.EmailStatus;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EmailManagerService {
    private final List<EmailProvider> emailProviders;
    private final EmailTemplateService templateService;
    private final EmailProperties emailProperties;
    private final EmailDeliveryLogService logService;
    private final MeterRegistry meterRegistry;
    private final TaskExecutor emailTaskExecutor;

    public EmailManagerService(
            List<EmailProvider> emailProviders,
            EmailTemplateService templateService,
            EmailProperties emailProperties,
            EmailDeliveryLogService logService,
            MeterRegistry meterRegistry,
            @Qualifier("emailTaskExecutor") TaskExecutor emailTaskExecutor) {

        this.emailProviders = emailProviders.stream()
                .sorted(Comparator.comparingInt(EmailProvider::getPriority))
                .collect(Collectors.toList());
        this.templateService = templateService;
        this.emailProperties = emailProperties;
        this.logService = logService;
        this.meterRegistry = meterRegistry;
        this.emailTaskExecutor = emailTaskExecutor;
    }

    @Async("emailTaskExecutor")
    public CompletableFuture<EmailDeliveryResult> sendEmailAsync(EmailRequest request) {
        return CompletableFuture.supplyAsync(() -> sendEmail(request), emailTaskExecutor);
    }

    public EmailDeliveryResult sendEmail(EmailRequest request) {
        validateEmailRequest(request);
        enrichEmailRequest(request);

        EmailDeliveryLog deliveryLog = createDeliveryLog(request);

        try {
            EmailDeliveryResult result = sendEmailWithRetry(request);
            logService.logSuccess(deliveryLog, result);
            return result;
        } catch (EmailException e) {
            logService.logFailure(deliveryLog, e);
            throw e;
        }
    }

    public EmailDeliveryResult sendEmailWithTemplate(
            String templateName, String recipient, String subject, Map<String, Object> templateVariables) {
        String htmlContent = templateService.processTemplate(templateName, templateVariables);

        EmailRequest request = EmailRequest.builder()
                .recipients(Collections.singletonList(recipient))
                .subject(subject)
                .html(htmlContent)
                .templateName(templateName)
                .templateVariables(templateVariables)
                .build();

        return sendEmail(request);
    }

    private EmailDeliveryResult sendEmailWithRetry(EmailRequest request) {
        List<EmailProvider> availableProviders =
                emailProviders.stream().filter(EmailProvider::isHealthy).toList();

        if (availableProviders.isEmpty()) {
            throw new EmailException("No healthy email providers available");
        }

        EmailException lastException = null;

        for (EmailProvider provider : availableProviders) {
            for (int attempt = 1; attempt <= emailProperties.getRetryAttempts(); attempt++) {
                try {
                    provider.sendEmail(request);
                    return EmailDeliveryResult.builder()
                            .success(true)
                            .provider(provider.getName())
                            .attempts(attempt)
                            .timestamp(LocalDateTime.now())
                            .build();

                } catch (EmailException e) {
                    lastException = e;
                    log.warn("Attempt {} failed for provider {}: {}", attempt, provider.getName(), e.getMessage());

                    if (attempt < emailProperties.getRetryAttempts()) {
                        try {
                            Thread.sleep(emailProperties.getRetryDelayMs() * attempt);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new EmailException("Email sending interrupted", ie);
                        }
                    }
                }
            }
        }

        throw new EmailException("Failed to send email using all providers", lastException);
    }

    private void validateEmailRequest(EmailRequest request) {
        if (request.getRecipients() == null || request.getRecipients().isEmpty()) {
            throw new EmailException("Recipients cannot be empty");
        }

        if (StringUtils.isBlank(request.getSubject())) {
            throw new EmailException("Subject cannot be empty");
        }

        if (StringUtils.isBlank(request.getHtml()) && StringUtils.isBlank(request.getText())) {
            throw new EmailException("Email content (HTML or text) cannot be empty");
        }
    }

    private void enrichEmailRequest(EmailRequest request) {
        if (StringUtils.isBlank(request.getFromAddress())) {
            request.setFromAddress(emailProperties.getDefaultFromAddress());
        }

        if (StringUtils.isBlank(request.getFromName())) {
            request.setFromName(emailProperties.getDefaultFromName());
        }
    }

    private EmailDeliveryLog createDeliveryLog(EmailRequest request) {
        return EmailDeliveryLog.builder()
                .recipients(String.join(",", request.getRecipients()))
                .subject(request.getSubject())
                .templateName(request.getTemplateName())
                .status(EmailStatus.PENDING)
                .createdAt(Instant.now())
                .build();
    }
}
