package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.arihant.sftp.ArihantSftpClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "arihantSftpService")
public class ArihantSftpService {

    private final OneclickProperties.ArihantSftpConfig config;
    private ArihantSftpClientImpl arihantSftpClient;

    public ArihantSftpService(OneclickProperties.ArihantSftpConfig config) {
        this.config = config;
        ArihantSftpClientImpl.ClientConfig clientConfig = new ArihantSftpClientImpl.ClientConfig(config);
        this.arihantSftpClient = new ArihantSftpClientImpl(clientConfig);
        log.info("Arihant SFTP Service initialized with client config: {}", clientConfig);
    }
}
